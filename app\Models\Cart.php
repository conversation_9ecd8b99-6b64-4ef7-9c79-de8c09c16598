<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    protected $fillable=['user_id','product_id','order_id','quantity','amount','price','status','is_packed','is_returned_by_picker','box','color','box_id','packed_quantity','backorder_quantity','original_cart_id','packed_at','picker_notes'];

    protected $casts = [
        'is_packed' => 'boolean',
        'is_returned_by_picker' => 'boolean',
    ];

    // public function product(){
    //     return $this->hasOne('App\Models\Product','id','product_id');
    // }
    // public static function getAllProductFromCart(){
    //     return Cart::with('product')->where('user_id',auth()->user()->id)->get();
    // }
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id')->with('item_colors');
    }

    public function order(){
        return $this->belongsTo(Order::class,'order_id');
    }

    public function color_name()
    {
        return $this->belongsTo(Color::class, 'color');
    }

    public function orderBox()
    {
        return $this->belongsTo(OrderBox::class, 'box_id');
    }

    public function getAvailableQuantityAttribute()
    {
        // Check product stock for this specific color
        $itemColor = \App\Models\ItemColor::where('product_id', $this->product_id)
                                         ->where('color_id', $this->color)
                                         ->first();

        return $itemColor ? $itemColor->stock : 0;
    }

    public function getPackedQuantityAttribute()
    {
        return $this->attributes['packed_quantity'] ?? $this->quantity;
    }

    public function getBackorderQuantityAttribute()
    {
        return $this->attributes['backorder_quantity'] ?? 0;
    }

    public function hasBackorder()
    {
        return $this->getBackorderQuantityAttribute() > 0;
    }

    public function canFulfillQuantity($requestedQty = null)
    {
        $qty = $requestedQty ?? $this->quantity;
        return $this->getAvailableQuantityAttribute() >= $qty;
    }

    public function createBackorder($unfulfillableQty, $reason = 'Stock shortage', $priority = 'normal')
    {
        $this->backorder_quantity = $unfulfillableQty;
        $this->packed_quantity = $this->quantity - $unfulfillableQty;
        $this->save();

        // Create backorder record
        return \App\Models\Backorder::create([
            'original_order_id' => $this->order_id,
            'cart_item_id' => $this->id,
            'product_id' => $this->product_id,
            'color_id' => $this->color,
            'quantity' => $unfulfillableQty,
            'price' => $this->price,
            'total_amount' => $unfulfillableQty * $this->price,
            'status' => 'pending',
            'reason' => $reason,
            'priority' => $priority,
            'created_by_picker_id' => auth()->id()
        ]);
    }
}
