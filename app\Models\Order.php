<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $fillable = ['order_number', 'user_id', 'salesman_id', 'sub_total', 'shipping_id', 'coupon', 'total_amount', 'amount_left', 'amount_paid','quantity', 'payment_method', 'payment_status', 'status', 'first_name', 'last_name', 'email', 'phone', 'country', 'post_code', 'address1', 'address2', 'is_draft', 'is_quick_order', 'original_order_id', 'signature_data', 'interaction_log_id', 'shipping_preference_id', 'return_status', 'credit_memo_id', 'delivery_instructions', 'shipping_method','delivery_method','picker_id'];

    protected $casts = [
        'is_draft' => 'boolean',
        'is_quick_order' => 'boolean',
    ];

    public function cart_info(){
        return $this->hasMany('App\Models\Cart','order_id','id')->where('status', '!=', 'distributed');
    }

    public function all_cart_items(){
        return $this->hasMany('App\Models\Cart','order_id','id');
    }
    public static function getAllOrder($id){
        return Order::with('cart_info')->find($id);
    }
    public static function countActiveOrder(){
        $data=Order::count();
        if($data){
            return $data;
        }
        return 0;
    }
    public function cart(){
        return $this->hasMany(Cart::class);
    }

    public function shipping(){
        return $this->belongsTo(Shipping::class,'shipping_id');
    }
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    public function salesman()
    {
        return $this->belongsTo(User::class, 'salesman_id');
    }

    public function signature()
    {
        return $this->hasOne(SignatureRecord::class);
    }

    public function interactionLog()
    {
        return $this->belongsTo(InteractionLog::class);
    }

    public function shippingPreference()
    {
        return $this->belongsTo(ShippingPreference::class);
    }

    public function route()
    {
        return $this->belongsToMany(DeliveryRoute::class, 'route_optimization_queue', 'order_id', 'route_id');
    }

    public function scopeNotDraft($query)
    {
        return $query->where('is_draft', false);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function customer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function orderPrices()
    {
        return $this->hasMany(OrderPrice::class);
    }

    public function histories()
    {
        return $this->hasMany(OrderHistory::class);
    }

    public function boxes()
    {
        return $this->hasMany(OrderBox::class);
    }

    public function backorders()
    {
        return $this->hasMany(Backorder::class, 'original_order_id');
    }

    public function picker()
    {
        return $this->belongsTo(User::class, 'picker_id');
    }

    public function getTotalBoxesAttribute()
    {
        return $this->boxes()->count();
    }

    public function getPackingProgressAttribute()
    {
        $totalItems = $this->cart_info->sum('quantity');
        $packedItems = $this->cart_info->sum('packed_quantity');

        if ($totalItems == 0) return 0;

        return round(($packedItems / $totalItems) * 100, 2);
    }

    public function hasBackorders()
    {
        return $this->backorders()->where('status', 'pending')->exists();
    }

    public function canBeCompleted()
    {
        try {
            // Check if all items are either packed or backordered
            $items = $this->cart_info;

            if ($items->isEmpty()) {
                \Log::warning('Attempting to complete order with no items', ['order_id' => $this->id]);
                return false;
            }

            foreach ($items as $item) {
                $packedQty = $item->packed_quantity ?? 0;
                $backorderQty = $item->backorder_quantity ?? 0;

                // Item is accounted for if it's packed, backordered, or returned
                $isAccountedFor = ($packedQty + $backorderQty) >= $item->quantity || $item->is_returned_by_picker;

                if (!$isAccountedFor) {
                    \Log::info('Order cannot be completed - item not fully accounted for', [
                        'order_id' => $this->id,
                        'item_id' => $item->id,
                        'required_qty' => $item->quantity,
                        'packed_qty' => $packedQty,
                        'backorder_qty' => $backorderQty,
                        'is_returned' => $item->is_returned_by_picker
                    ]);
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Error checking if order can be completed', [
                'order_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    public function completeOrder()
    {
        try {
            if (!$this->canBeCompleted()) {
                throw new \Exception('Order cannot be completed. Some items are not packed or backordered.');
            }

            // Additional validation
            if ($this->status === 'shipped') {
                throw new \Exception('Order is already completed.');
            }

            if ($this->status === 'cancelled') {
                throw new \Exception('Cannot complete a cancelled order.');
            }

            // Update order status
            $this->status = 'shipped';
            $this->save();

            // Update all boxes as sealed
            $this->boxes()->update(['is_sealed' => true, 'sealed_at' => now()]);

            // Log the completion
            \Log::info('Order completed successfully', [
                'order_id' => $this->id,
                'order_number' => $this->order_number,
                'completed_by' => auth()->id(),
                'completed_at' => now()
            ]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Error completing order', [
                'order_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

}
