<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the return_status enum to include additional statuses
        DB::statement("ALTER TABLE orders MODIFY COLUMN return_status ENUM('none', 'requested', 'approved', 'rejected', 'processed', 'partial_return_with_backorder', 'full_return', 'partial_return') DEFAULT 'none'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE orders MODIFY COLUMN return_status ENUM('none', 'requested', 'approved', 'rejected', 'processed') DEFAULT 'none'");
    }
};
