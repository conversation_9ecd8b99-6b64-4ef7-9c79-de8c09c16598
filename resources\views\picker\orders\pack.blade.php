@extends('backend.layouts.master')

@section('title','Lamart || Pack Order')

@section('main-content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Pack Order #{{$order->order_number}}</h5>
        <div>
            <span class="badge badge-info">{{$order->status}}</span>
            <button class="btn btn-info btn-sm ml-2" data-toggle="modal" data-target="#helpModal">
                <i class="fas fa-question-circle"></i> Help
            </button>
            <a href="{{route('picker.orders.show', $order->id)}}" class="btn btn-secondary btn-sm ml-2">
                <i class="fas fa-arrow-left"></i> Back to Order
            </a>
            {{-- <button class="btn btn-warning btn-sm ml-2" id="cleanupBtn" title="Clean up duplicate items">
                <i class="fas fa-broom"></i> Cleanup Items
            </button>
            <button class="btn btn-secondary btn-sm ml-2" id="cleanupBoxesBtn" title="Clean up duplicate boxes">
                <i class="fas fa-boxes"></i> Fix Boxes
            </button>
            <button class="btn btn-info btn-sm ml-2" id="debugBoxesBtn" title="Debug box data">
                <i class="fas fa-bug"></i> Debug
            </button>
            <button class="btn btn-success btn-sm ml-2" id="completeOrderBtn">
                <i class="fas fa-check"></i> Complete Order
            </button> --}}
        </div>
    </div>

    <!-- Order Progress Indicator -->
    <div class="card-body border-bottom bg-light">
        <div class="row">
            <div class="col-md-12">
                <h6 class="text-muted mb-2">
                    <i class="fas fa-chart-line"></i> Packing Progress
                </h6>
                @php
                    $totalItems = $order->cart_info->count();
                    $packedItems = $order->cart_info->where('is_packed', 1)->count();
                    $backorderItems = $order->cart_info->where('backorder_quantity', '>', 0)->count();
                    $returnedItems = $order->cart_info->where('is_returned_by_picker', 1)->count();
                    $progressPercentage = $totalItems > 0 ? round((($packedItems + $backorderItems + $returnedItems) / $totalItems) * 100) : 0;
                @endphp
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar"
                         style="width: {{$progressPercentage}}%"
                         id="packing-progress-bar">
                        <span class="progress-text font-weight-bold">{{$progressPercentage}}% Complete</span>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="card border-0 bg-transparent">
                            <div class="card-body p-2">
                                <i class="fas fa-list text-muted"></i>
                                <div class="font-weight-bold" id="total-items">{{$totalItems}}</div>
                                <small class="text-muted">Total Items</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card border-0 bg-transparent">
                            <div class="card-body p-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <div class="font-weight-bold text-success" id="packed-items">{{$packedItems}}</div>
                                <small class="text-success">Packed</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card border-0 bg-transparent">
                            <div class="card-body p-2">
                                <i class="fas fa-clock text-warning"></i>
                                <div class="font-weight-bold text-warning" id="backorder-items">{{$backorderItems}}</div>
                                <small class="text-warning">Backorders</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card border-0 bg-transparent">
                            <div class="card-body p-2">
                                <i class="fas fa-undo text-danger"></i>
                                <div class="font-weight-bold text-danger" id="returned-items">{{$returnedItems}}</div>
                                <small class="text-danger">Returned</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($order)

        <!-- Customer Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Customer Information</h6>
                        <p class="mb-1"><strong>{{$order->first_name.' '.$order->last_name}}</strong></p>
                        <p class="mb-1">{{$order->email}}</p>
                        <p class="mb-1">{{$order->phone}}</p>
                        <p class="mb-0">{{$order->address1}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Order Summary</h6>
                        <p class="mb-1"><strong>Total Items:</strong> {{$order->cart_info->count()}}</p>
                        <p class="mb-1"><strong>Total Amount:</strong> ${{number_format($order->total_amount,2)}}</p>
                        <p class="mb-1"><strong>Delivery Method:</strong> {{ucfirst($order->delivery_method ?? 'N/A')}}</p>
                        <p class="mb-0"><strong>Order Date:</strong> {{$order->created_at->format('M d, Y')}}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Smart Packing Interface -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> How to Pack This Order</h5>
                    <p class="mb-0">
                        <strong>Step 1:</strong> Create boxes by clicking "Add New Box"
                        <strong>Step 2:</strong> For each item, enter how many go in each box
                        <strong>Step 3:</strong> Click "Pack Item" when ready
                        <strong>Step 4:</strong> Print labels when all items are packed
                    </p>
                </div>
            </div>
        </div>

        <!-- Box Management -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes"></i> Shipping Boxes
                            <button class="btn btn-light btn-sm float-right" id="addBoxBtn">
                                <i class="fas fa-plus"></i> Add New Box
                            </button>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="row no-gutters" id="boxContainer">
                            @foreach($order->boxes as $index => $box)
                            <div class="col-md-4">
                                <div class="box-preview-card border-right" data-box-id="{{$box->id}}" data-box-number="{{$box->box_number}}">
                                    <div class="box-header p-3 bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-box text-primary"></i>
                                                <strong>{{$box->box_label}}</strong>
                                                @if($box->is_full)
                                                    <span class="badge badge-warning ml-2">FULL</span>
                                                @endif
                                            </h6>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary select-box" data-box-id="{{$box->id}}" title="Use this box">
                                                    <i class="fas fa-hand-pointer"></i>
                                                </button>
                                                <button class="btn btn-outline-info print-box-label" data-box-id="{{$box->id}}" title="Print label">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="box-content p-3">
                                        <!-- Box Items Preview -->
                                        <div class="box-items-preview mb-3">
                                            <h6 class="text-muted mb-2">
                                                <i class="fas fa-cube"></i> Items in this box:
                                                <span class="badge badge-info badge-sm ml-2" id="itemCount{{$box->id}}">{{$box->items->count()}}</span>
                                            </h6>
                                            <div class="items-list" id="itemsList{{$box->id}}">
                                                @if($box->items->count() > 0)
                                                    @foreach($box->items as $packedItem)
                                                        <div class="item-preview packed-item" id="item-{{$packedItem->id}}-box-{{$box->id}}">
                                                            <i class="fas fa-cube"></i>
                                                            {{$packedItem->product->title ?? 'Unknown Item'}}
                                                            <span class="badge badge-success badge-sm">{{$packedItem->packed_quantity ?? $packedItem->quantity}} items</span>
                                                            @if($packedItem->color_name)
                                                                <br><small class="text-muted">Color: {{$packedItem->color_name->name}}</small>
                                                            @endif
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="text-muted text-center py-2">
                                                        <i class="fas fa-inbox"></i><br>
                                                        <small>No items added yet</small>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Box Dimensions -->
                                        <div class="box-dimensions mb-3">
                                            <h6 class="text-muted mb-2">Box Size (inches):</h6>
                                            <div class="row">
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm box-dimension text-center"
                                                           placeholder="Length" value="{{$box->length ?? 12}}" data-box-id="{{$box->id}}" data-field="length">
                                                    <small class="text-muted">Length</small>
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm box-dimension text-center"
                                                           placeholder="Width" value="{{$box->width ?? 8}}" data-box-id="{{$box->id}}" data-field="width">
                                                    <small class="text-muted">Width</small>
                                                </div>
                                                <div class="col-4">
                                                    <input type="number" class="form-control form-control-sm box-dimension text-center"
                                                           placeholder="Height" value="{{$box->height ?? 6}}" data-box-id="{{$box->id}}" data-field="height">
                                                    <small class="text-muted">Height</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Box Weight -->
                                        <div class="box-weight mb-3">
                                            <h6 class="text-muted mb-2">Weight (lbs):</h6>
                                            <input type="number" class="form-control form-control-sm box-weight text-center"
                                                   placeholder="0.0" value="{{$box->weight ?? 0}}" data-box-id="{{$box->id}}" step="0.1">
                                        </div>
                                    </div>

                                    <div class="box-footer p-3 bg-light border-top">
                                        @if(!$box->is_full)
                                        <button class="btn btn-warning btn-sm btn-block mark-full" data-box-id="{{$box->id}}">
                                            <i class="fas fa-lock"></i> Mark This Box Full
                                        </button>
                                        @else
                                        <button class="btn btn-success btn-sm btn-block" disabled>
                                            <i class="fas fa-check"></i> Box is Full
                                        </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <div class="p-3 bg-light border-top">
                            <button class="btn btn-success btn-lg btn-block" id="printAllLabelsBtn">
                                <i class="fas fa-print"></i> Print All Box Labels
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Smart Items Packing Interface -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-list"></i> Items to Pack
                            <small class="float-right">{{$order->cart_info->count()}} items total</small>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($order->cart_info as $index => $item)
                        @php
                            $availableQty = $item->getAvailableQuantityAttribute();
                            $packedQty = $item->packed_quantity ?? 0;
                            $backorderQty = $item->backorder_quantity ?? 0;
                            $remainingQty = $item->quantity - $packedQty;
                        @endphp
                        <div class="item-packing-card border-bottom" data-item-id="{{$item->id}}">
                            <div class="row no-gutters">
                                <!-- Item Info -->
                                <div class="col-md-4 p-4 bg-light">
                                    <div class="item-info">
                                        <div class="d-flex align-items-center mb-3">
                                            @if($item->product->photo)
                                            <img src="{{$item->product->photo}}" class="img-fluid mr-3 rounded" style="max-width:60px" alt="Product">
                                            @endif
                                            <div>
                                                <h6 class="mb-1">{{$item->product->title}}</h6>
                                                <small class="text-muted">Item #: {{$item->product->item_number ?? 'N/A'}}</small><br>
                                                <small class="text-muted">Color: {{$item->color_name->name ?? 'N/A'}}</small>
                                            </div>
                                        </div>

                                        <div class="quantity-info">
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="quantity-box">
                                                        <div class="h4 mb-0 text-primary">{{$item->quantity}}</div>
                                                        <small class="text-muted">Ordered</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="quantity-box">
                                                        <div class="h4 mb-0 {{$availableQty >= $item->quantity ? 'text-success' : 'text-danger'}}">{{$availableQty}}</div>
                                                        <small class="text-muted">Available</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="quantity-box">
                                                        <div class="h4 mb-0 text-info">{{$remainingQty}}</div>
                                                        <small class="text-muted">To Pack</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        @if($availableQty < $item->quantity)
                                        <div class="alert alert-warning mt-3 mb-0">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>Low Stock!</strong><br>
                                            Missing {{$item->quantity - $availableQty}} items
                                        </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Box Distribution -->
                                <div class="col-md-5 p-4">
                                    <h6 class="mb-3">
                                        <i class="fas fa-boxes"></i> Distribute Across Boxes
                                    </h6>

                                    <div class="box-distribution" id="boxDistribution{{$item->id}}">
                                        @foreach($order->boxes as $box)
                                        <div class="box-quantity-row mb-2" data-box-id="{{$box->id}}">
                                            <div class="row align-items-center">
                                                <div class="col-6">
                                                    <label class="mb-0">
                                                        <i class="fas fa-box text-primary"></i>
                                                        {{$box->box_label}}
                                                    </label>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group input-group-sm">
                                                        <input type="number"
                                                               class="form-control box-quantity-input"
                                                               data-item-id="{{$item->id}}"
                                                               data-box-id="{{$box->id}}"
                                                               min="0"
                                                               max="{{min($item->quantity, $availableQty)}}"
                                                               value="0"
                                                               placeholder="0">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">items</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach

                                        <div class="distribution-summary mt-3 p-2 bg-light rounded">
                                            <small class="text-muted">
                                                Total distributed: <span class="total-distributed" data-item-id="{{$item->id}}">0</span> / {{min($item->quantity, $availableQty)}}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="col-md-3 p-4 bg-light border-left">
                                    <h6 class="mb-3">Actions</h6>

                                    <div class="action-buttons">
                                        <button class="btn btn-success btn-block mb-2 pack-distributed-item" data-item-id="{{$item->id}}"
                                                {{$item->is_packed ? 'disabled' : ''}}>
                                            <i class="fas fa-check"></i>
                                            {{$item->is_packed ? 'Already Packed' : 'Pack Item'}}
                                        </button>

                                        @if($availableQty < $item->quantity)
                                        <button class="btn btn-warning btn-block mb-2 smart-backorder" data-item-id="{{$item->id}}"
                                                data-shortage="{{$item->quantity - $availableQty}}">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Create Backorder
                                        </button>
                                        @endif

                                        <button class="btn btn-danger btn-block return-item" data-item-id="{{$item->id}}"
                                                {{$item->is_returned_by_picker ? 'disabled' : ''}}>
                                            <i class="fas fa-undo"></i>
                                            {{$item->is_returned_by_picker ? 'Returned' : 'Return Item'}}
                                        </button>
                                    </div>

                                    <div class="item-status mt-3">
                                        <h6 class="mb-2">Status:</h6>
                                        <div class="status-display" id="statusDisplay{{$item->id}}">
                                            @if($item->is_packed)
                                                <span class="badge badge-success badge-lg">✓ Packed</span>
                                                @if($item->orderBox)
                                                    <div class="mt-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-box"></i> In: <strong>{{$item->orderBox->box_label}}</strong>
                                                        </small>
                                                    </div>
                                                @endif
                                            @elseif($item->is_returned_by_picker)
                                                <span class="badge badge-danger badge-lg">↩ Returned</span>
                                            @elseif($backorderQty > 0)
                                                <span class="badge badge-warning badge-lg">⚠ Partial</span>
                                            @else
                                                <span class="badge badge-secondary badge-lg">⏳ Pending</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between">
                    <a href="{{route('picker.orders')}}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                    <div>
                        <button class="btn btn-info" onclick="printPackingList()">
                            <i class="fas fa-print"></i> Print Packing List
                        </button>
                        <button class="btn btn-success" id="completeOrderBtn">
                            <i class="fas fa-check-circle"></i> Complete Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle text-info"></i> Packing Help Guide
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary"><i class="fas fa-box"></i> Step 1: Manage Boxes</h6>
                        <ul class="text-muted">
                            <li>Click <strong>"Add Box"</strong> to create shipping boxes</li>
                            <li>Enter box dimensions (Length × Width × Height)</li>
                            <li>Set weight limits for each box</li>
                            <li>You can create multiple boxes for large orders</li>
                        </ul>

                        <h6 class="text-primary"><i class="fas fa-clipboard-check"></i> Step 2: Pack Items</h6>
                        <ul class="text-muted">
                            <li>Check the <strong>"Available"</strong> column for stock levels</li>
                            <li>Adjust <strong>"Pack Qty"</strong> if needed</li>
                            <li>Select which <strong>"Box"</strong> to put each item in</li>
                            <li>Click <strong>"Pack"</strong> to mark item as packed</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary"><i class="fas fa-exclamation-triangle"></i> Step 3: Handle Low Stock</h6>
                        <ul class="text-muted">
                            <li>Items with <span class="badge badge-danger">Low Stock</span> warnings need attention</li>
                            <li>Pack what's available</li>
                            <li>Click <strong>"Backorder"</strong> for missing items</li>
                            <li>System will automatically create backorders</li>
                        </ul>

                        <h6 class="text-primary"><i class="fas fa-print"></i> Step 4: Print & Complete</h6>
                        <ul class="text-muted">
                            <li>Print individual box labels or all at once</li>
                            <li>Labels show "Box 1 of 3", "Box 2 of 3", etc.</li>
                            <li>Click <strong>"Complete Order"</strong> when done</li>
                            <li>System validates all items are processed</li>
                        </ul>
                    </div>
                </div>

                <hr>

                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb"></i> Quick Tips:</h6>
                    <ul class="mb-0">
                        <li><strong>Red warnings</strong> = Low stock items that need backorders</li>
                        <li><strong>Box selection</strong> = Choose which box each item goes in</li>
                        <li><strong>Pack quantity</strong> = How many you're actually packing (can be less than ordered)</li>
                        <li><strong>Return items</strong> = Use for damaged or problematic items</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Got it!</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
    /* Modern Box Preview Cards */
    .box-preview-card {
        border: 2px solid #e3e6f0;
        transition: all 0.3s ease;
        background: white;
        min-height: 400px;
    }
    .box-preview-card.selected {
        border-color: #4e73df;
        box-shadow: 0 0 15px rgba(78, 115, 223, 0.3);
    }
    .box-preview-card:hover {
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    /* Item Packing Cards */
    .item-packing-card {
        transition: all 0.3s ease;
    }
    .item-packing-card:hover {
        background-color: #f8f9fc;
    }

    /* Quantity Boxes */
    .quantity-box {
        padding: 10px;
        border-radius: 8px;
        background: white;
        border: 1px solid #e3e6f0;
        margin-bottom: 5px;
    }

    /* Box Distribution */
    .box-quantity-row {
        padding: 8px;
        border-radius: 6px;
        background: white;
        border: 1px solid #e3e6f0;
    }
    .box-quantity-row:hover {
        background: #f8f9fc;
    }

    /* Distribution Summary */
    .distribution-summary {
        border: 1px solid #d1ecf1;
        background: #d1ecf1 !important;
    }

    /* Status Badges */
    .badge-lg {
        font-size: 0.9rem;
        padding: 8px 12px;
    }

    /* Action Buttons */
    .action-buttons .btn {
        font-weight: 500;
        border-radius: 6px;
    }

    /* Box Items Preview */
    .items-list {
        max-height: 150px;
        overflow-y: auto;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px;
        background: white;
    }

    .item-preview {
        padding: 4px 8px;
        margin-bottom: 4px;
        background: #f8f9fc;
        border-radius: 4px;
        font-size: 0.85rem;
        border-left: 3px solid #4e73df;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .item-packing-card .row {
            flex-direction: column;
        }
        .quantity-box {
            margin-bottom: 10px;
        }
    }

    /* Loading States */
    .btn.loading {
        position: relative;
        color: transparent;
    }
    .btn.loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
@endsection

@push('styles')
<style>
    .table td, .table th {
        vertical-align: middle;
    }
    .box-item {
        display: inline-block;
        margin-right: 10px;
    }
    .box-item.active {
        background-color: #e3f2fd;
        padding: 5px;
        border-radius: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize distribution summaries
    $('.item-packing-card').each(function() {
        const itemId = $(this).data('item-id');
        updateDistributionSummary(itemId);
    });

    // Load existing box contents
    loadExistingBoxContents();
});

// Load existing box contents from server
function loadExistingBoxContents() {
    @foreach($order->boxes as $box)
        @foreach($box->items as $item)
            updateBoxPreview({{$box->id}}, {{$item->id}});
            // Auto-fill the distribution input for packed items
            autoFillPackedItemDistribution({{$item->id}}, {{$box->id}}, {{$item->packed_quantity ?? $item->quantity}});
        @endforeach
    @endforeach
}

// Auto-fill distribution inputs for already packed items
function autoFillPackedItemDistribution(itemId, boxId, quantity) {
    const input = $(`.box-quantity-input[data-item-id="${itemId}"][data-box-id="${boxId}"]`);
    if (input.length > 0) {
        input.val(quantity);
        input.prop('readonly', true);
        input.addClass('bg-light');
        input.closest('.box-quantity-row').addClass('border-success');

        // Add a small indicator
        if (!input.siblings('.packed-indicator').length) {
            input.after('<small class="packed-indicator text-success ml-2"><i class="fas fa-check"></i> Packed</small>');
        }

        // Update the distribution summary
        updateDistributionSummary(itemId);
    }
}

// Add new box to interface dynamically
function addNewBoxToInterface(box) {
    console.log('Adding new box to interface:', box);

    if (!box || !box.id) {
        console.error('Invalid box data provided to addNewBoxToInterface');
        return;
    }

    // Check if box already exists to prevent duplicates
    if ($(`.box-preview-card[data-box-id="${box.id}"]`).length > 0) {
        console.warn('Box already exists in interface, skipping duplicate');
        return;
    }

    const boxCount = $('.box-preview-card').length + 1;
    console.log('Creating box HTML with ID:', box.id);

    const newBoxHtml = `
        <div class="col-md-4">
            <div class="box-preview-card border-right" data-box-id="${box.id}" data-box-number="${boxCount}">
                <div class="box-header p-3 bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-box text-primary"></i>
                            <strong>Box ${boxCount}</strong>
                        </h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary select-box" data-box-id="${box.id}" title="Use this box">
                                <i class="fas fa-hand-pointer"></i>
                            </button>
                            <button class="btn btn-outline-info print-box-label" data-box-id="${box.id}" title="Print label">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="box-content p-3">
                    <div class="box-items-preview mb-3">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-cube"></i> Items in this box:
                        </h6>
                        <div class="items-list" id="itemsList${box.id}">
                            <div class="text-muted text-center py-2">
                                <i class="fas fa-inbox"></i><br>
                                <small>No items added yet</small>
                            </div>
                        </div>
                    </div>

                    <div class="box-dimensions mb-3">
                        <h6 class="text-muted mb-2">Box Size (inches):</h6>
                        <div class="row">
                            <div class="col-4">
                                <input type="number" class="form-control form-control-sm box-dimension text-center"
                                       placeholder="Length" value="${box.length || 12}" data-box-id="${box.id}" data-field="length">
                                <small class="text-muted">Length</small>
                            </div>
                            <div class="col-4">
                                <input type="number" class="form-control form-control-sm box-dimension text-center"
                                       placeholder="Width" value="${box.width || 8}" data-box-id="${box.id}" data-field="width">
                                <small class="text-muted">Width</small>
                            </div>
                            <div class="col-4">
                                <input type="number" class="form-control form-control-sm box-dimension text-center"
                                       placeholder="Height" value="${box.height || 6}" data-box-id="${box.id}" data-field="height">
                                <small class="text-muted">Height</small>
                            </div>
                        </div>
                    </div>

                    <div class="box-weight mb-3">
                        <h6 class="text-muted mb-2">Weight (lbs):</h6>
                        <input type="number" class="form-control form-control-sm box-weight text-center"
                               placeholder="0.0" value="${box.weight || 0}" data-box-id="${box.id}" step="0.1">
                    </div>
                </div>

                <div class="box-footer p-3 bg-light border-top">
                    <button class="btn btn-warning btn-sm btn-block mark-full" data-box-id="${box.id}">
                        <i class="fas fa-lock"></i> Mark This Box Full
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add to box container
    $('#boxContainer').append(newBoxHtml);

    // Verify the box was added correctly
    const addedBox = $(`.box-preview-card[data-box-id="${box.id}"]`);
    if (addedBox.length > 0) {
        console.log('Box added successfully to DOM');

        // Verify data attributes on input fields
        const dimensionInputs = addedBox.find('.box-dimension');
        const weightInput = addedBox.find('.box-weight');

        console.log('Dimension inputs found:', dimensionInputs.length);
        console.log('Weight input found:', weightInput.length);

        dimensionInputs.each(function() {
            const inputBoxId = $(this).data('box-id');
            console.log('Dimension input box-id:', inputBoxId);
            if (!inputBoxId) {
                console.error('Missing box-id on dimension input:', this);
            }
        });

        const weightBoxId = weightInput.data('box-id');
        console.log('Weight input box-id:', weightBoxId);
        if (!weightBoxId) {
            console.error('Missing box-id on weight input:', weightInput[0]);
        }
    } else {
        console.error('Failed to add box to DOM');
    }

    // Add box option to all item distribution selects
    $('.box-distribution').each(function() {
        const itemId = $(this).attr('id').replace('boxDistribution', '');
        const itemCard = $(`.item-packing-card[data-item-id="${itemId}"]`);
        const orderedQty = parseInt(itemCard.find('.quantity-box .h4').first().text()) || 0;
        const availableQty = parseInt(itemCard.find('.quantity-box .h4').eq(1).text()) || 0;
        const maxQty = Math.min(orderedQty, availableQty) || 0;

        const newBoxOption = `
            <div class="box-quantity-row mb-2" data-box-id="${box.id}">
                <div class="row align-items-center">
                    <div class="col-6">
                        <label class="mb-0">
                            <i class="fas fa-box text-primary"></i>
                            ${box.box_label}
                        </label>
                    </div>
                    <div class="col-6">
                        <div class="input-group input-group-sm">
                            <input type="number"
                                   class="form-control box-quantity-input"
                                   data-item-id="${itemId}"
                                   data-box-id="${box.id}"
                                   min="0"
                                   max="${maxQty}"
                                   value="0"
                                   placeholder="0">
                            <div class="input-group-append">
                                <span class="input-group-text">items</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $(this).find('.distribution-summary').before(newBoxOption);
    });
}

    // Add new box
    $('#addBoxBtn').click(function() {
        const button = $(this);

        // Prevent double-clicking
        if (button.prop('disabled')) {
            return;
        }

        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Creating...');

        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: "{{route('picker.orders.create-box')}}",
            type: 'POST',
            data: {
                "_token": token,
                "order_id": {{$order->id}},
                "max_weight": 50
            },
            success: function(response) {
                if(response.status && response.box) {
                    // Check if box already exists to prevent duplicates
                    if ($(`.box-preview-card[data-box-id="${response.box.id}"]`).length > 0) {
                        console.warn('Box already exists in interface, skipping duplicate');
                        Swal.fire('Info', 'Box already exists', 'info');
                        return;
                    }

                    // Validate box data before adding to interface
                    if (response.box.id && response.box.box_label) {
                        // Add new box dynamically without reloading
                        addNewBoxToInterface(response.box);
                        Swal.fire('Success!', 'New box created successfully!', 'success');
                    } else {
                        console.error('Invalid box data received:', response.box);
                        Swal.fire('Error!', 'Invalid box data received from server', 'error');
                    }
                } else {
                    Swal.fire('Error!', response.message || 'Unknown error occurred', 'error');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to create box';
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if(xhr.responseJSON && xhr.responseJSON.errors) {
                    errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                }

                console.error('Box creation error:', xhr);
                Swal.fire('Error!', errorMessage, 'error');
            },
            complete: function() {
                // Re-enable button
                button.prop('disabled', false).html('<i class="fas fa-plus"></i> Add New Box');
            }
        });
    });

    // Select box
    $(document).on('click', '.select-box', function() {
        const boxId = $(this).data('box-id');
        const boxLabel = $(this).closest('.box-card').find('.font-weight-bold').text();

        $('#currentBoxId').val(boxId);
        $('#currentBoxDisplay').text(boxLabel);

        $('.box-card').removeClass('border-primary');
        $(this).closest('.box-card').addClass('border-primary');

        // Update all box selects to this box
        $('.box-select').val(boxId);
    });

    // Update box dimensions and weight
    $(document).on('change', '.box-dimension, .box-weight', function() {
        let boxId = $(this).data('box-id');
        const field = $(this).data('field') || 'weight';
        const value = $(this).val();
        const token = $("meta[name='csrf-token']").attr("content");

        // Handle case where boxId might be a string representation of a number
        if (typeof boxId === 'string' && !isNaN(boxId) && boxId.trim() !== '') {
            boxId = parseInt(boxId.trim());
        }

        console.log('Box update triggered:', {
            boxId: boxId,
            boxIdType: typeof boxId,
            field: field,
            value: value,
            element: this,
            allDataAttributes: $(this).data(),
            dataBoxIdAttribute: $(this).attr('data-box-id'),
            dataFieldAttribute: $(this).attr('data-field')
        });

        // Validate box ID - be more lenient but still catch real issues
        if (!boxId || boxId === 'undefined' || boxId === 'null' || boxId === '' || isNaN(boxId)) {
            console.error('Invalid box ID:', boxId, 'Type:', typeof boxId);
            console.error('Element that triggered:', this);
            console.error('Element data attributes:', $(this).data());

            // Only show error if it's truly invalid, not just a type issue
            if (!boxId || boxId === 'undefined' || boxId === 'null' || boxId === '') {
                Swal.fire('Error!', 'Invalid box ID. Please refresh the page and try again.', 'error');
                return;
            }
        }

        // Convert to number to ensure consistency
        const numericBoxId = parseInt(boxId);
        if (isNaN(numericBoxId) || numericBoxId <= 0) {
            console.error('Box ID is not a valid positive number:', boxId);
            Swal.fire('Error!', 'Invalid box ID format. Please refresh the page and try again.', 'error');
            return;
        }

        let data = {
            "_token": token
        };
        data[field] = value;

        $.ajax({
            url: `/picker/orders/boxes/${numericBoxId}/update`,
            type: 'PUT',
            data: data,
            success: function(response) {
                if(response.status) {
                    console.log('Box updated successfully:', response);
                    // Optionally show a subtle success indicator
                    // $(this).addClass('border-success').removeClass('border-danger');
                } else {
                    console.error('Box update failed:', response);
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function(xhr) {
                console.error('Box update AJAX error:', xhr);
                console.error('Request details:', {
                    url: `/picker/orders/boxes/${numericBoxId}/update`,
                    boxId: numericBoxId,
                    field: field,
                    value: value
                });

                let errorMessage = 'Failed to update box';
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                // Only show error if it's not a validation issue that might resolve itself
                if (xhr.status !== 422) {
                    Swal.fire('Error!', errorMessage, 'error');
                } else {
                    console.warn('Validation error, but data might still be saved:', errorMessage);
                }
            }
        });
    });

    // Mark box as full
    $(document).on('click', '.mark-full', function() {
        const boxId = $(this).data('box-id');
        const token = $("meta[name='csrf-token']").attr("content");
        const button = $(this);

        Swal.fire({
            title: 'Mark Box as Full?',
            text: 'This will prevent adding more items to this box.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, mark it full',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

                $.ajax({
                    url: `/picker/orders/boxes/${boxId}/update`,
                    type: 'PUT',
                    data: {
                        "_token": token,
                        "is_full": "1"  // Send as string to avoid boolean validation issues
                    },
                    success: function(response) {
                        if(response.status) {
                            // Update button state without reloading
                            button.removeClass('btn-warning').addClass('btn-success')
                                  .prop('disabled', true)
                                  .html('<i class="fas fa-check"></i> Box is Full');

                            // Add full badge to box header
                            const boxCard = button.closest('.box-preview-card');
                            const boxHeader = boxCard.find('h6 strong');
                            if (!boxHeader.find('.badge-warning').length) {
                                boxHeader.append(' <span class="badge badge-warning ml-2">FULL</span>');
                            }

                            Swal.fire('Success!', 'Box marked as full!', 'success');
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                            button.prop('disabled', false).html('<i class="fas fa-lock"></i> Mark This Box Full');
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Failed to update box';
                        if(xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if(xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                        }

                        Swal.fire('Error!', errorMessage, 'error');
                        button.prop('disabled', false).html('<i class="fas fa-lock"></i> Mark This Box Full');
                    }
                });
            }
        });
    });

    // Print box label
    $(document).on('click', '.print-box-label', function() {
        const boxId = $(this).data('box-id');
        window.open(`/picker/orders/boxes/${boxId}/label`, '_blank');
    });

    // Print all labels
    $('#printAllLabelsBtn').click(function() {
        const boxIds = [];
        $('.box-preview-card').each(function() {
            const boxId = $(this).data('box-id');
            if (boxId) {
                boxIds.push(boxId);
            }
        });

        if (boxIds.length === 0) {
            Swal.fire('No Boxes', 'Please create some boxes first', 'info');
            return;
        }

        Swal.fire({
            title: 'Print All Labels',
            html: `
                <p>Choose how you want to print ${boxIds.length} label(s):</p>
                <div style="margin: 20px 0;">
                    <button class="btn btn-primary btn-block mb-2" id="printAllInOne">
                        <i class="fas fa-file-pdf"></i> Print All in One Document
                    </button>
                    <button class="btn btn-secondary btn-block" id="printSeparate">
                        <i class="fas fa-external-link-alt"></i> Print Separate Windows
                    </button>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: 'Cancel',
            didOpen: () => {
                // Print all in one document
                $('#printAllInOne').click(function() {
                    window.open(`/picker/orders/{{$order->id}}/all-labels`, '_blank');
                    Swal.close();
                });

                // Print in separate windows
                $('#printSeparate').click(function() {
                    boxIds.forEach((boxId, index) => {
                        setTimeout(() => {
                            window.open(`/picker/orders/boxes/${boxId}/label`, '_blank');
                        }, index * 500); // 500ms delay between each window
                    });
                    Swal.close();
                    Swal.fire('Success!', `Opening ${boxIds.length} label(s) for printing`, 'success');
                });
            }
        });
    });

    // Print all labels
    $('#printAllLabelsBtn').click(function() {
        @foreach($order->boxes as $box)
            window.open(`/picker/orders/boxes/{{$box->id}}/label`, '_blank');
        @endforeach
    });

    // Handle box quantity input changes
    $(document).on('input', '.box-quantity-input', function() {
        const itemId = $(this).data('item-id');
        updateDistributionSummary(itemId);
        updateBoxPreview($(this).data('box-id'), itemId);
    });

    // Pack distributed item
    $(document).on('click', '.pack-distributed-item', function() {
        const itemId = $(this).data('item-id');
        const distributions = getItemDistribution(itemId);

        if (distributions.total === 0) {
            Swal.fire('Error!', 'Please distribute items to boxes first', 'error');
            return;
        }

        // Pack items across multiple boxes
        packItemDistribution(itemId, distributions);
    });

    // Smart backorder handling
    $(document).on('click', '.smart-backorder', function() {
        const itemId = $(this).data('item-id');
        const shortage = $(this).data('shortage');

        Swal.fire({
            title: 'Create Backorder?',
            html: `
                <div class="text-left">
                    <p>This item has insufficient stock:</p>
                    <ul>
                        <li><strong>Missing:</strong> ${shortage} items</li>
                        <li><strong>Action:</strong> Create backorder for missing items</li>
                        <li><strong>Result:</strong> Available items will be packed, missing items backordered</li>
                    </ul>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, create backorder',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                createSmartBackorder(itemId, shortage);
            }
        });
    });

    // Get item distribution across boxes
    function getItemDistribution(itemId) {
        const distributions = {};
        let total = 0;

        $(`.box-quantity-input[data-item-id="${itemId}"]`).each(function() {
            const boxId = $(this).data('box-id');
            const quantity = parseInt($(this).val()) || 0;
            if (quantity > 0) {
                distributions[boxId] = quantity;
                total += quantity;
            }
        });

        return { distributions, total };
    }

    // Update distribution summary
    function updateDistributionSummary(itemId) {
        const { total } = getItemDistribution(itemId);
        $(`.total-distributed[data-item-id="${itemId}"]`).text(total);

        // Update button state
        const button = $(`.pack-distributed-item[data-item-id="${itemId}"]`);
        if (total > 0) {
            button.removeClass('btn-secondary').addClass('btn-success').prop('disabled', false);
        } else {
            button.removeClass('btn-success').addClass('btn-secondary').prop('disabled', true);
        }
    }

    // Update box preview
    function updateBoxPreview(boxId, itemId) {
        const quantity = $(`.box-quantity-input[data-item-id="${itemId}"][data-box-id="${boxId}"]`).val();
        const itemsList = $(`#itemsList${boxId}`);
        const itemPreviewId = `item-${itemId}-box-${boxId}`;

        // Remove existing preview
        $(`#${itemPreviewId}`).remove();

        if (quantity && quantity > 0) {
            // Add new preview
            const itemName = $(`.item-packing-card[data-item-id="${itemId}"] h6`).text();
            const itemPreview = `
                <div class="item-preview" id="${itemPreviewId}">
                    <i class="fas fa-cube"></i> ${itemName} (${quantity} items)
                </div>
            `;

            if (itemsList.find('.item-preview').length === 0) {
                itemsList.html(itemPreview);
            } else {
                itemsList.append(itemPreview);
            }
        }

        // Update empty state
        if (itemsList.find('.item-preview').length === 0) {
            itemsList.html(`
                <div class="text-muted text-center py-2">
                    <i class="fas fa-inbox"></i><br>
                    <small>No items added yet</small>
                </div>
            `);
        }
    }

    // Pack item distribution across multiple boxes
    function packItemDistribution(itemId, distributions) {
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: "{{route('picker.orders.pack-distributed-item')}}",
            type: 'POST',
            data: {
                "_token": token,
                "item_id": itemId,
                "distributions": JSON.stringify(distributions.distributions)
            },
            success: function(response) {
                if(response.status) {
                    // Update UI
                    const statusDisplay = $(`#statusDisplay${itemId}`);
                    statusDisplay.html('<span class="badge badge-success badge-lg">✓ Packed</span>');

                    // Disable pack button
                    $(`.pack-distributed-item[data-item-id="${itemId}"]`)
                        .prop('disabled', true)
                        .html('<i class="fas fa-check"></i> Already Packed');

                    Swal.fire('Success!', response.message, 'success');

                    // Refresh box previews
                    Object.keys(distributions.distributions).forEach(boxId => {
                        updateBoxItemCount(boxId);
                    });
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to pack item';
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error!', errorMessage, 'error');
            }
        });
    }

    // Create smart backorder
    function createSmartBackorder(itemId, shortage) {
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: "{{route('picker.orders.create-smart-backorder')}}",
            type: 'POST',
            data: {
                "_token": token,
                "item_id": itemId,
                "backorder_quantity": shortage
            },
            success: function(response) {
                if(response.status) {
                    Swal.fire('Success!', response.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'Failed to create backorder', 'error');
            }
        });
    }

    // Update box item count
    function updateBoxItemCount(boxId) {
        // This would typically make an AJAX call to get updated counts
        // For now, we'll update the UI based on current state
    }

    // Return item
    $(document).on('click', '.return-item', function() {
        const itemId = $(this).data('item-id');
        const button = $(this);

        Swal.fire({
            title: 'Return Item?',
            html: `
                <div class="text-left">
                    <p><strong>⚠️ Important:</strong> Returning this item will:</p>
                    <ul>
                        <li>Mark this item as returned</li>
                        <li><strong>Convert ALL remaining unpacked items to backorders</strong></li>
                        <li>Change the entire order status to "returned"</li>
                    </ul>
                    <div class="form-group mt-3">
                        <label for="returnNotes">Return Notes (optional):</label>
                        <textarea id="returnNotes" class="form-control" rows="3" placeholder="Reason for return..."></textarea>
                    </div>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, return item & backorder rest',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#d33'
        }).then((result) => {
            if (result.isConfirmed) {
                const notes = document.getElementById('returnNotes').value;
                returnItemToOffice(itemId, button, notes);
            }
        });
    });

    // Return item to office function
    function returnItemToOffice(itemId, button, notes = '') {
        const token = $("meta[name='csrf-token']").attr("content");

        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

        $.ajax({
            url: "{{route('picker.orders.update-item-status')}}",
            type: 'POST',
            data: {
                "_token": token,
                "item_id": itemId,
                "is_returned": "true",
                "picker_notes": notes
            },
            success: function(response) {
                if(response.status) {
                    Swal.fire({
                        title: 'Order Returned Successfully!',
                        html: `
                            <div class="text-left">
                                <p><strong>✓ Item "${response.returned_item}" has been returned</strong></p>
                                <p><strong>✓ ${response.backorders_created} remaining items converted to backorders</strong></p>
                                <p><strong>✓ Order status changed to: ${response.order_status}</strong></p>
                                <hr>
                                <p class="text-muted">The page will refresh to show the updated status.</p>
                            </div>
                        `,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = "{{ url('/picker/orders') }}"
                    });
                } else {
                    Swal.fire('Error!', response.message, 'error');
                    button.prop('disabled', false).html('<i class="fas fa-undo"></i> Return Item');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to return item';
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error!', errorMessage, 'error');
                button.prop('disabled', false).html('<i class="fas fa-undo"></i> Return Item');
            }
        });
    }

    // Create backorder
    $(document).on('click', '.create-backorder', function() {
        const itemId = $(this).data('item-id');
        const shortage = $(this).data('shortage');

        Swal.fire({
            title: 'Create Backorder?',
            text: `This will create a backorder for ${shortage} items due to insufficient stock.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, create backorder',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const token = $("meta[name='csrf-token']").attr("content");

                $.ajax({
                    url: "{{route('picker.orders.create-backorder')}}",
                    type: 'POST',
                    data: {
                        "_token": token,
                        "item_id": itemId,
                        "backorder_quantity": shortage
                    },
                    success: function(response) {
                        if(response.status) {
                            Swal.fire('Success!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Failed to create backorder', 'error');
                    }
                });
            }
        });
    });

    // Update item status
    function updateItemStatus(itemId, data, button) {
        const token = $("meta[name='csrf-token']").attr("content");

        // Show loading state
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

        $.ajax({
            url: "{{route('picker.orders.update-item-status')}}",
            type: 'POST',
            data: {
                "_token": token,
                "item_id": itemId,
                ...data
            },
            success: function(response) {
                if(response.status) {
                    // Update UI
                    const row = button.closest('tr');
                    if(data.is_packed === 'true' || data.is_packed === true) {
                        row.find('.status-cell').html('<span class="badge badge-success">Packed</span>');
                        row.find('.pack-item').prop('disabled', true).html('<i class="fas fa-check"></i> Packed');
                        row.find('.return-item').prop('disabled', true);
                    } else if(data.is_returned === 'true' || data.is_returned === true) {
                        row.find('.status-cell').html('<span class="badge badge-danger">Returned</span>');
                        row.find('.return-item').prop('disabled', true).html('<i class="fas fa-undo"></i> Returned');
                        row.find('.pack-item').prop('disabled', true);
                    }

                    Swal.fire('Success!', response.message, 'success');

                    // Check if backorder was created
                    if(response.backorder_created) {
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                } else {
                    Swal.fire('Error!', response.message, 'error');
                    // Reset button
                    button.prop('disabled', false);
                    if(data.is_packed) {
                        button.html('<i class="fas fa-check"></i> Pack');
                    } else if(data.is_returned) {
                        button.html('<i class="fas fa-undo"></i> Return');
                    }
                }
            },
            error: function(xhr) {
                let errorMessage = 'Something went wrong';
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if(xhr.responseJSON && xhr.responseJSON.errors) {
                    errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                }

                Swal.fire('Error!', errorMessage, 'error');

                // Reset button
                button.prop('disabled', false);
                if(data.is_packed) {
                    button.html('<i class="fas fa-check"></i> Pack');
                } else if(data.is_returned) {
                    button.html('<i class="fas fa-undo"></i> Return');
                }
            }
        });
    }

    // Cleanup duplicate items
    $('#cleanupBtn').click(function() {
        Swal.fire({
            title: 'Clean Up Duplicates?',
            text: 'This will remove any duplicate items that may have been created during packing.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, clean up',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const token = $("meta[name='csrf-token']").attr("content");

                $.ajax({
                    url: "{{route('picker.orders.cleanup-duplicates')}}",
                    type: 'POST',
                    data: {
                        "_token": token,
                        "order_id": {{$order->id}}
                    },
                    success: function(response) {
                        if(response.status) {
                            if(response.cleaned_count > 0) {
                                Swal.fire('Success!', response.message, 'success').then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Info', 'No duplicate items found to clean up', 'info');
                            }
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Failed to clean up duplicates', 'error');
                    }
                });
            }
        });
    });

    // Cleanup duplicate boxes
    $('#cleanupBoxesBtn').click(function() {
        Swal.fire({
            title: 'Fix Box Issues?',
            html: `
                <div class="text-left">
                    <p>This will:</p>
                    <ul>
                        <li>Remove any duplicate boxes</li>
                        <li>Renumber boxes sequentially</li>
                        <li>Fix box labels (Box 1 of X, etc.)</li>
                        <li>Preserve all box contents and settings</li>
                    </ul>
                    <p><strong>This action is safe and will not delete your data.</strong></p>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, fix boxes',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const token = $("meta[name='csrf-token']").attr("content");

                $.ajax({
                    url: "{{route('picker.orders.cleanup-boxes')}}",
                    type: 'POST',
                    data: {
                        "_token": token,
                        "order_id": {{$order->id}}
                    },
                    success: function(response) {
                        if(response.status) {
                            if(response.duplicates_removed > 0) {
                                Swal.fire('Success!', response.message, 'success').then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Info', 'No duplicate boxes found. Boxes are already properly organized.', 'info');
                            }
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Failed to cleanup boxes', 'error');
                    }
                });
            }
        });
    });

    // Debug boxes
    $('#debugBoxesBtn').click(function() {
        console.log('=== BOX DEBUG INFO ===');

        const boxInfo = [];
        $('.box-preview-card').each(function() {
            const boxCard = $(this);
            const boxId = boxCard.data('box-id');
            const boxNumber = boxCard.data('box-number');

            const dimensionInputs = boxCard.find('.box-dimension');
            const weightInput = boxCard.find('.box-weight');

            const boxData = {
                cardBoxId: boxId,
                cardBoxNumber: boxNumber,
                dimensionInputs: [],
                weightInput: null
            };

            dimensionInputs.each(function() {
                const input = $(this);
                boxData.dimensionInputs.push({
                    field: input.data('field'),
                    boxId: input.data('box-id'),
                    value: input.val(),
                    dataBoxIdAttr: input.attr('data-box-id'),
                    dataFieldAttr: input.attr('data-field')
                });
            });

            if (weightInput.length > 0) {
                boxData.weightInput = {
                    boxId: weightInput.data('box-id'),
                    value: weightInput.val(),
                    dataBoxIdAttr: weightInput.attr('data-box-id')
                };
            }

            boxInfo.push(boxData);
        });

        console.log('Box Information:', boxInfo);

        // Show in a modal
        const debugHtml = `
            <div style="text-align: left; font-family: monospace; font-size: 12px;">
                <h6>Box Debug Information:</h6>
                <pre>${JSON.stringify(boxInfo, null, 2)}</pre>
                <p><strong>Check the browser console for detailed logs.</strong></p>
            </div>
        `;

        Swal.fire({
            title: 'Box Debug Info',
            html: debugHtml,
            width: '80%',
            confirmButtonText: 'Close'
        });
    });

    // Complete order
    $('#completeOrderBtn').click(function() {
        // First check order progress
        $.get(`/picker/orders/{{$order->id}}/progress`, function(response) {
            if (response.status && response.progress.can_complete) {
                Swal.fire({
                    title: 'Complete Order?',
                    html: `
                        <div class="text-left">
                            <p><strong>Order Summary:</strong></p>
                            <ul>
                                <li>Total Items: ${response.progress.total_items}</li>
                                <li>Packed Items: ${response.progress.packed_items}</li>
                                <li>Backorder Items: ${response.progress.backorder_items}</li>
                                <li>Total Boxes: ${response.progress.total_boxes}</li>
                                <li>Progress: ${response.progress.progress_percentage}%</li>
                            </ul>
                            <p>This will mark the order as shipped and seal all boxes.</p>
                        </div>
                    `,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, complete order!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        completeOrder();
                    }
                });
            } else {
                Swal.fire({
                    title: 'Cannot Complete Order',
                    text: 'Some items are not packed or backordered. Please complete all items before finishing the order.',
                    icon: 'error'
                });
            }
        });
    });

    function completeOrder() {
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: `/picker/orders/{{$order->id}}/complete`,
            type: 'POST',
            data: {
                "_token": token
            },
            success: function(response) {
                if(response.status) {
                    Swal.fire({
                        title: 'Order Completed!',
                        html: `
                            <div class="text-left">
                                <p>Order has been successfully completed:</p>
                                <ul>
                                    <li>Status: ${response.order_status}</li>
                                    <li>Total Boxes: ${response.total_boxes}</li>
                                    <li>Backorders Created: ${response.has_backorders ? 'Yes' : 'No'}</li>
                                </ul>
                            </div>
                        `,
                        icon: 'success'
                    }).then(() => {
                        window.location.href = "{{route('picker.orders')}}";
                    });
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'Failed to complete order', 'error');
            }
        });
    }

    function printPackingList() {
        window.open("{{route('picker.orders.packing-list', $order->id)}}", '_blank');
    }

    // Update progress indicator
    function updateProgressIndicator() {
        const totalItems = parseInt($('#total-items').text());

        // Count packed items (look for "✓ Packed" badge)
        const packedItems = $('.item-packing-card').filter(function() {
            return $(this).find('.badge-success').text().includes('✓ Packed');
        }).length;

        // Count backorder items (look for "⚠ Partial" badge)
        const backorderItems = $('.item-packing-card').filter(function() {
            return $(this).find('.badge-warning').text().includes('⚠ Partial');
        }).length;

        // Count returned items (look for "↩ Returned" badge)
        const returnedItems = $('.item-packing-card').filter(function() {
            return $(this).find('.badge-danger').text().includes('↩ Returned');
        }).length;

        const completedItems = packedItems + backorderItems + returnedItems;
        const progressPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

        // Update progress bar
        $('#packing-progress-bar').css('width', progressPercentage + '%');
        $('#packing-progress-bar .progress-text').text(progressPercentage + '% Complete');

        // Update counters
        $('#packed-items').text(packedItems);
        $('#backorder-items').text(backorderItems);
        $('#returned-items').text(returnedItems);

        // Change progress bar color based on completion
        $('#packing-progress-bar').removeClass('bg-success bg-warning bg-info');
        if (progressPercentage === 100) {
            $('#packing-progress-bar').addClass('bg-success');
        } else if (progressPercentage >= 50) {
            $('#packing-progress-bar').addClass('bg-info');
        } else {
            $('#packing-progress-bar').addClass('bg-warning');
        }

        console.log('Progress Update:', {
            totalItems,
            packedItems,
            backorderItems,
            returnedItems,
            progressPercentage
        });
    }

    // Call updateProgressIndicator after any packing action
    $(document).ready(function() {
        updateProgressIndicator();

        // Update progress after any AJAX success that changes item status
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url.includes('create-backorder')) {
                // For backorder operations, refresh the page to show updated status
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else if (settings.url.includes('pack-distributed-item')) {
                // For pack operations, just update the progress indicator
                setTimeout(updateProgressIndicator, 500);
            }
            // For return operations, the page already refreshes in the success handler
        });
    });
</script>
@endpush
